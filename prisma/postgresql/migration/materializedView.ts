import { prismaPG } from '../../../src/config/db';
export const migrateMaterializedView = async () => {
  await prismaPG.$transaction(async (txn) => {
    await txn.$executeRaw`
      CREATE SCHEMA IF NOT EXISTS "leaderboard";
    `;
    (await Promise.all([
      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."ContributionWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."contributionScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."contributionScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (CURRENT_DATE - INTERVAL '7 days')
      ORDER BY
        p."contributionScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,
      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."QnAAnswerWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."qnaAnswerScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."qnaAnswerScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (CURRENT_DATE - INTERVAL '7 days')
      ORDER BY
        p."qnaAnswerScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,
      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."TroubleshootWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."troubleshootAnswerScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."troubleshootAnswerScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (CURRENT_DATE - INTERVAL '7 days')
      ORDER BY
        p."troubleshootAnswerScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,
      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."TotalWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."totalScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."totalScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (CURRENT_DATE - INTERVAL '7 days')
      ORDER BY
        p."totalScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,
      txn.$executeRaw`
        CREATE MATERIALIZED VIEW "leaderboard"."ContributionOverallLeaderboard"
        AS
        SELECT
          r."profileId",
          r."contributionScore" AS score,
          ROW_NUMBER OVER (ORDER BY r."contributionScore" DESC) AS rank
        FROM
          "score"."RewardProfile"
        ORDER BY r."contributionScore" DESC
        LIMIT 20
      `,
      txn.$executeRaw`
        CREATE MATERIALIZED VIEW "leaderboard"."QnAAnswerOverallLeaderboard"
        AS
        SELECT
          r."profileId",
          r."qnaAnswerScore" AS score,
          ROW_NUMBER OVER (ORDER BY r."qnaAnswerScore" DESC) AS rank
        FROM
          "score"."RewardProfile"
        ORDER BY r."qnaAnswerScore" DESC
        LIMIT 20
      `,
      txn.$executeRaw`
        CREATE MATERIALIZED VIEW "leaderboard"."TroubleshootOverallLeaderboard"
        AS
        SELECT
          r."profileId",
          r."troubleshootAnswerScore" AS score,
          ROW_NUMBER OVER (ORDER BY r."troubleshootAnswerScore" DESC) AS rank
        FROM
          "score"."RewardProfile"
        ORDER BY r."troubleshootAnswerScore" DESC
        LIMIT 20
      `,
      txn.$executeRaw`
        CREATE MATERIALIZED VIEW "leaderboard"."TotalOverallLeaderboard"
        AS
        SELECT
          r."profileId",
          r."totalScore" AS score,
          ROW_NUMBER OVER (ORDER BY r."totalScore" DESC) AS rank
        FROM
          "score"."RewardProfile"
        ORDER BY r."totalScore" DESC
        LIMIT 20
      `,
    ]))
  })

}
