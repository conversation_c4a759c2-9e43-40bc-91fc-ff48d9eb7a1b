import { PlaceSourceE } from '@consts/place/place';
import { CountryIso2Schema } from '@schemas/common/common';
import { LatitudeSchema, LongitudeSchema } from '@schemas/port/common';
import z from 'zod';

export const AnnouncementCreateOneSchema = z.object({
  title: z.string().min(3).max(100),
  description: z.string().min(3).max(1000),
  latitude: LatitudeSchema,
  longitude: LongitudeSchema,
  source: PlaceSourceE,
  addressMapBoxId: z.string(),
  addressText: z.string(),
  cityMapBoxId: z.string(),
  cityName: z.string(),
  countryIso2: CountryIso2Schema,
});
export type AnnouncementCreateOneI = z.infer<typeof AnnouncementCreateOneSchema>;
