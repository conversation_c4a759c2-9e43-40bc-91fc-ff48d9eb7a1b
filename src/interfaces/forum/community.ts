import { MemberTypeI } from "@consts/forum/member";

export type FetchCommunityClientI = {
  id: string;
  cursorId: string;
  name: string;
  description:string;
  access: string;
  isRestricted: boolean;
  memberCount: number;
  questionCount: number;
  CommunityMember: Array<{
    profileId: string;
    type: MemberTypeI;
  }>;
};
export type CommunityFetchOneForExternalClientResultI = {
  id: string;
};
