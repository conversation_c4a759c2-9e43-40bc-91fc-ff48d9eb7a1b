import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import {
  // CommunityFetchOneForExternalClientResultI,
  FetchCommunityClientI,
} from '@interfaces/forum/community';
import StorageModule from '@modules/storage';
import { Prisma } from '@prisma/postgres';
import type {
  // ProfileIdRouteParamsI,
  RouteParamsI,
} from '@schemas/common/common';
import type {
  CommunityCreateOneI,
  CommunityFetchForClientI,
  CommunityUpdateOneI,
  //  CommunityOneForExternalClientParamsI
} from '@schemas/forum/community';
import type { GlobalSearchParamsI } from '@schemas/forum/question';
import type { GlobalSearchCommunityItemI, GlobalSearchResponseI } from '@interfaces/forum/search';
import { errorHandler } from '@utils/errors/handler';
import CommunityMemberModule from '../member';

export const CommunityModule = {
  isPermitted: async ({ communityId, profileId }: { communityId: string; profileId: string }): Promise<void> => {
    const communityResult = await CommunityModule.fetchById({ id: communityId });
    if (communityResult?.access === 'PRIVATE') {
      const _memberResult = await CommunityMemberModule.fetchMember({ communityId, profileId });
    }
    return;
  },
  fetchById: async (
    { id }: Pick<Prisma.CommunityWhereUniqueInput, 'id'>,
    select: Prisma.CommunitySelect = {
      access: true,
      isRestricted: true,
    },
  ) => {
    const communityResult = await prismaPG.community.findUnique({
      select,
      where: {
        id,
      },
    });
    if (!communityResult) {
      throw new AppError('CMTY007');
    }
    return communityResult;
  },
  createOne: async (state: FastifyStateI, { name, access, isRestricted, avatar }: CommunityCreateOneI) => {
    try {
      const selfProfileId = state.profileId;
      const result = await prismaPG.$transaction(async (txn) => {
        const communityInput: Prisma.CommunityUncheckedCreateInput = {
          name,
          access,
          isRestricted,
          creatorId: selfProfileId,
          CommunityMember: {
            create: {
              profileId: selfProfileId,
              type: 'ADMIN',
            },
          },
        };
        if (avatar?.fileUrl) {
          communityInput.avatar = avatar.fileUrl;
        }
        const communityResult = await txn.community.create({
          data: communityInput,
        });
        if (!communityResult) {
          throw new AppError('CMTY001');
        }
        return {
          ...communityResult,
          cursorId: communityResult.cursorId.toString(),
        };
      });
      return result;
    } catch (error) {
      errorHandler(error);
    }
  },
  updateOne: async (
    state: FastifyStateI,
    params: CommunityUpdateOneI,
    { id: communityId }: RouteParamsI,
  ): Promise<FetchCommunityClientI> => {
    const profileId = state.profileId;

    const communityMemberResult = await prismaPG.communityMember.findFirst({
      where: {
        profileId: profileId,
        communityId,
      },
    });
    if (communityMemberResult.type !== 'ADMIN') {
      throw new AppError('CMTY004');
    }

    const existingCommunityResult = await CommunityModule.fetchById(
      { id: communityId },
      {
        id: true,
        avatar: true,
      },
    );

    if (params?.avatar?.opr) {
      if (params.avatar.opr === 'UPDATE' && params?.avatar?.fileUrl && existingCommunityResult.avatar) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingCommunityResult.avatar });
        } catch (_error) {
          // console.log(_error);
        }
      } else if (params.avatar.opr === 'DELETE' && existingCommunityResult.avatar) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingCommunityResult.avatar });
        } catch (_error) {
          // console.error(_error);
        }
      }
    }

    const updateParams: Prisma.CommunityUpdateInput = {};

    if (params?.name) {
      updateParams.name = params.name;
    }
    if (params?.access) {
      updateParams.access = params.access;
    }
    if (params?.isRestricted) {
      updateParams.isRestricted = params.isRestricted;
    }

    if (params?.avatar?.opr) {
      if (params.avatar.opr === 'CREATE' || params.avatar.opr === 'UPDATE') {
        if (params?.avatar?.fileUrl) {
          updateParams.avatar = params.avatar.fileUrl;
        }
      } else if (params.avatar.opr === 'DELETE') {
        updateParams.avatar = null;
      }
    }

    const communityResult = await prismaPG.community.update({
      data: updateParams,
      where: {
        id: communityId,
      },
      select: {
        id: true,
        cursorId: true,
        name: true,
        access: true,
        isRestricted: true,
        memberCount: true,
        questionCount: true,
        avatar: true,
      },
    });
    return {
      ...communityResult,
      cursorId: communityResult.cursorId.toString(),
    };
  },
  deleteOne: async (state: FastifyStateI, filtersP: RouteParamsI) => {
    try {
      const profileId = state.profileId;
      const communityMemberResult = await prismaPG.communityMember.findFirst({
        where: {
          profileId: profileId,
          communityId: filtersP.id,
        },
      });
      if (communityMemberResult.type !== 'ADMIN') {
        throw new AppError('CMTY004');
      }
      // const adminListTemp = await prismaPG.communityMember.findMany({
      //   where:{
      //     communityId:filtersP.id,
      //     type:'ADMIN'
      //   },
      //   select:{
      //     profileId:true
      //   }
      // })
      // const adminList = adminListTemp.map(item => item.profileId)
      // if(adminList.length < 1){
      //   throw new AppError('CMTY005');
      // }
      const communityResult = await prismaPG.community.delete({
        where: { id: filtersP.id },
        select: { id: true },
      });

      if (!communityResult) {
        throw new AppError('CMTY003');
      }

      return communityResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchForClient: async ({ name, cursorId, pageSize }: CommunityFetchForClientI): Promise<FetchCommunityClientI[]> => {
    const searchName = name?.trim().toLowerCase() ?? '';

    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }
    const result: FetchCommunityClientI[] = await prismaPG.$queryRaw<FetchCommunityClientI[]>`
      SELECT
      c."id",
      c."cursorId"::text AS "cursorId",
      c."name",
      c."memberCount",
      c."questionCount"
      FROM forum."Community" c
      WHERE
        ${searchName ? Prisma.sql`c."name" ILIKE ${searchName + '%'}` : Prisma.sql`1 = 1`}
      ${cursorId ? Prisma.sql`AND c."cursorId" < ${cursorId}` : Prisma.empty}
    ORDER BY c."cursorId" DESC
     LIMIT ${pageSize}
    `;
    return result;
  },
  globalSearch: async (
    state: FastifyStateI,
    { search, page, pageSize }: GlobalSearchParamsI,
  ): Promise<GlobalSearchResponseI<GlobalSearchCommunityItemI>> => {
    const selfProfileId = state.profileId;
    const offset = page * pageSize;
    const searchTerm = `%${search.toLowerCase()}%`;

    // const tsSearchTerm = search.replace(/[!&|():*]/g, ' ').trim().split(/\s+/).join(' & ');

    try {
      const [totalResult, communitiesResult] = await Promise.all([
        prismaPG.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(DISTINCT c."id") as count
          FROM "forum"."Community" c
          LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
          WHERE (
            (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
            OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
          )
          AND c."name" ILIKE ${searchTerm}
        `,
        prismaPG.$queryRaw<GlobalSearchCommunityItemI[]>`
          WITH ranked_communities AS (
            SELECT DISTINCT c."id",
            CASE
              WHEN c."name" ILIKE ${searchTerm} THEN 1
              ELSE 2
            END AS relevance_score,
            c."createdAt"
            FROM "forum"."Community" c
            LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
            WHERE (
              (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
              OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
            )
            AND c."name" ILIKE ${searchTerm}
          )
          SELECT
            c."id",
            c."name",
            NULL as "description",
            c."access",
            c."isRestricted",
            c."memberCount",
            c."questionCount",
            ARRAY[]::text[] as "matchedFields"
          FROM "forum"."Community" c
          INNER JOIN ranked_communities rc ON c."id" = rc."id"
          ORDER BY rc.relevance_score ASC, rc."createdAt" DESC
          LIMIT ${pageSize}
          OFFSET ${offset}
        `,
      ]);

      const total = Number(totalResult?.[0]?.count || 0);

      const data = communitiesResult.map((item) => {
        const matchedFields: string[] = [];
        const searchLower = search.toLowerCase();

        if (item.name && item.name.toLowerCase().includes(searchLower)) {
          matchedFields.push('name');
        }

        return {
          ...item,
          matchedFields,
        };
      });

      return {
        data,
        total,
      };
    } catch (error) {
      errorHandler(error);
      throw error;
    }
  },
};
